#!/bin/bash

# NS Shop - Comprehensive Test Execution Script
# <PERSON><PERSON><PERSON> tất cả tests cho hệ thống Audit Logs & Notifications

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to run tests with error handling
run_test_suite() {
    local test_name="$1"
    local test_command="$2"
    local optional="$3"
    
    print_status "Running $test_name..."
    
    if eval "$test_command"; then
        print_success "$test_name completed successfully"
        return 0
    else
        if [ "$optional" = "optional" ]; then
            print_warning "$test_name failed (optional)"
            return 0
        else
            print_error "$test_name failed"
            return 1
        fi
    fi
}

# Main execution
main() {
    print_status "Starting comprehensive test execution for NS Shop Audit & Notifications"
    print_status "=================================================================="
    
    # Check if required dependencies are installed
    print_status "Checking dependencies..."
    
    if ! command -v npm &> /dev/null; then
        print_error "npm is not installed"
        exit 1
    fi
    
    if ! command -v npx &> /dev/null; then
        print_error "npx is not installed"
        exit 1
    fi
    
    print_success "Dependencies check passed"
    
    # Setup test environment
    print_status "Setting up test environment..."
    
    # Set test environment variables
    export NODE_ENV=test
    export DATABASE_URL="postgresql://test:test@localhost:5432/ns_shop_test"
    
    # Install dependencies if needed
    if [ ! -d "node_modules" ]; then
        print_status "Installing dependencies..."
        npm install
    fi
    
    # Setup test database
    print_status "Setting up test database..."
    if command -v npm run test:db:setup &> /dev/null; then
        npm run test:db:setup
    else
        print_warning "Test database setup script not found, skipping..."
    fi
    
    print_success "Test environment setup completed"
    
    # Test execution counters
    local total_tests=0
    local passed_tests=0
    local failed_tests=0
    
    # 1. Unit Tests
    print_status "Phase 1: Unit Tests"
    print_status "==================="
    
    total_tests=$((total_tests + 4))
    
    # Audit Logger Unit Tests
    if run_test_suite "Audit Logger Unit Tests" "npm test tests/unit/lib/audit-logger.test.ts"; then
        passed_tests=$((passed_tests + 1))
    else
        failed_tests=$((failed_tests + 1))
    fi
    
    # Notification Rules Unit Tests
    if run_test_suite "Notification Rules Unit Tests" "npm test tests/unit/lib/notification-rules.test.ts"; then
        passed_tests=$((passed_tests + 1))
    else
        failed_tests=$((failed_tests + 1))
    fi
    
    # Email Service Unit Tests
    if run_test_suite "Email Service Unit Tests" "npm test tests/unit/lib/email-service.test.ts"; then
        passed_tests=$((passed_tests + 1))
    else
        failed_tests=$((failed_tests + 1))
    fi
    
    # Audit Middleware Unit Tests
    if run_test_suite "Audit Middleware Unit Tests" "npm test tests/unit/lib/audit-middleware.test.ts"; then
        passed_tests=$((passed_tests + 1))
    else
        failed_tests=$((failed_tests + 1))
    fi
    
    # 2. Integration Tests
    print_status "Phase 2: Integration Tests"
    print_status "=========================="
    
    total_tests=$((total_tests + 3))
    
    # Audit Logs API Integration Tests
    if run_test_suite "Audit Logs API Integration Tests" "npm test tests/integration/api/admin/audit-logs.test.ts"; then
        passed_tests=$((passed_tests + 1))
    else
        failed_tests=$((failed_tests + 1))
    fi
    
    # Notifications API Integration Tests
    if run_test_suite "Notifications API Integration Tests" "npm test tests/integration/api/admin/notifications.test.ts"; then
        passed_tests=$((passed_tests + 1))
    else
        failed_tests=$((failed_tests + 1))
    fi
    
    # Audit Middleware Integration Tests
    if run_test_suite "Audit Middleware Integration Tests" "npm test tests/integration/middleware/audit-middleware.test.ts"; then
        passed_tests=$((passed_tests + 1))
    else
        failed_tests=$((failed_tests + 1))
    fi
    
    # 3. Security Tests
    print_status "Phase 3: Security Tests"
    print_status "======================="
    
    total_tests=$((total_tests + 2))
    
    # Audit Logs Security Tests
    if run_test_suite "Audit Logs Security Tests" "npm test tests/security/audit-logs-security.test.ts"; then
        passed_tests=$((passed_tests + 1))
    else
        failed_tests=$((failed_tests + 1))
    fi
    
    # Notifications Security Tests
    if run_test_suite "Notifications Security Tests" "npm test tests/security/notifications-security.test.ts"; then
        passed_tests=$((passed_tests + 1))
    else
        failed_tests=$((failed_tests + 1))
    fi
    
    # 4. Performance Tests (Optional - may take longer)
    print_status "Phase 4: Performance Tests (Optional)"
    print_status "====================================="
    
    total_tests=$((total_tests + 2))
    
    # Audit Logs Performance Tests
    if run_test_suite "Audit Logs Performance Tests" "npm test tests/performance/audit-logs-performance.test.ts" "optional"; then
        passed_tests=$((passed_tests + 1))
    else
        failed_tests=$((failed_tests + 1))
    fi
    
    # Notifications Performance Tests
    if run_test_suite "Notifications Performance Tests" "npm test tests/performance/notifications-performance.test.ts" "optional"; then
        passed_tests=$((passed_tests + 1))
    else
        failed_tests=$((failed_tests + 1))
    fi
    
    # 5. E2E Tests (Optional - requires browser setup)
    print_status "Phase 5: E2E Tests (Optional)"
    print_status "============================="
    
    # Check if Playwright is installed
    if command -v npx playwright --version &> /dev/null; then
        total_tests=$((total_tests + 2))
        
        # Install browsers if needed
        print_status "Installing Playwright browsers..."
        npx playwright install --with-deps
        
        # Audit Logs E2E Tests
        if run_test_suite "Audit Logs E2E Tests" "npx playwright test tests/e2e/admin-audit-logs.spec.ts" "optional"; then
            passed_tests=$((passed_tests + 1))
        else
            failed_tests=$((failed_tests + 1))
        fi
        
        # Notifications E2E Tests
        if run_test_suite "Notifications E2E Tests" "npx playwright test tests/e2e/admin-notifications.spec.ts" "optional"; then
            passed_tests=$((passed_tests + 1))
        else
            failed_tests=$((failed_tests + 1))
        fi
    else
        print_warning "Playwright not found, skipping E2E tests"
    fi
    
    # 6. Generate Test Coverage Report
    print_status "Phase 6: Test Coverage Report"
    print_status "============================="
    
    if run_test_suite "Coverage Report Generation" "npm test -- --coverage --testPathPattern='(unit|integration)'" "optional"; then
        print_success "Coverage report generated"
    else
        print_warning "Coverage report generation failed"
    fi
    
    # Final Summary
    print_status "Test Execution Summary"
    print_status "====================="
    
    echo -e "Total Test Suites: $total_tests"
    echo -e "${GREEN}Passed: $passed_tests${NC}"
    echo -e "${RED}Failed: $failed_tests${NC}"
    
    local success_rate=$((passed_tests * 100 / total_tests))
    echo -e "Success Rate: $success_rate%"
    
    if [ $failed_tests -eq 0 ]; then
        print_success "All tests passed! 🎉"
        print_success "The Audit Logs & Notifications system is ready for production."
        exit 0
    elif [ $success_rate -ge 80 ]; then
        print_warning "Most tests passed ($success_rate%). Review failed tests before deployment."
        exit 0
    else
        print_error "Too many tests failed ($success_rate% success rate). System needs attention."
        exit 1
    fi
}

# Help function
show_help() {
    echo "NS Shop - Comprehensive Test Execution Script"
    echo ""
    echo "Usage: $0 [options]"
    echo ""
    echo "Options:"
    echo "  -h, --help     Show this help message"
    echo "  --unit-only    Run only unit tests"
    echo "  --no-e2e       Skip E2E tests"
    echo "  --no-perf      Skip performance tests"
    echo "  --coverage     Generate coverage report"
    echo ""
    echo "Examples:"
    echo "  $0                    # Run all tests"
    echo "  $0 --unit-only       # Run only unit tests"
    echo "  $0 --no-e2e --no-perf # Skip E2E and performance tests"
}

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        -h|--help)
            show_help
            exit 0
            ;;
        --unit-only)
            UNIT_ONLY=true
            shift
            ;;
        --no-e2e)
            NO_E2E=true
            shift
            ;;
        --no-perf)
            NO_PERF=true
            shift
            ;;
        --coverage)
            COVERAGE=true
            shift
            ;;
        *)
            print_error "Unknown option: $1"
            show_help
            exit 1
            ;;
    esac
done

# Run main function
main
