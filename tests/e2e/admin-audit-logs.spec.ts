/**
 * Admin Audit Logs E2E Tests
 * Kiểm tra E2E cho trang quản lý audit logs
 */

import { test, expect } from "@playwright/test";
import { loginAsAdmin, loginAsModerator } from "./helpers/auth.helper";
import { AuditLogsHelper } from "./helpers/audit-logs.helper";
// Note: These helper functions would need to be implemented in test-data.helper.ts
// import { createTestAuditLogInDB, cleanupTestData } from "./helpers/test-data.helper";

test.describe("Admin Audit Logs E2E Tests", () => {
  let auditLogsHelper: AuditLogsHelper;

  test.beforeEach(async ({ page }) => {
    auditLogsHelper = new AuditLogsHelper(page);

    // Login as admin
    await loginAsAdmin(page);

    // Clean up any existing test data
    await cleanupTestData();
  });

  test.afterEach(async () => {
    // Clean up test data after each test
    await cleanupTestData();
  });

  test.describe("Audit Logs Viewing", () => {
    test("should display audit logs list", async () => {
      // Create test audit logs
      await createTestAuditLogInDB({
        action: "CREATE",
        resource: "Product",
        resourceId: "product-1",
        description: "Created test product",
        adminId: "test-admin-id",
      });

      await createTestAuditLogInDB({
        action: "UPDATE",
        resource: "User",
        resourceId: "user-1",
        description: "Updated user profile",
        adminId: "test-admin-id",
      });

      await auditLogsHelper.goto();

      // Verify audit logs are displayed
      const count = await auditLogsHelper.getAuditLogsCount();
      expect(count).toBeGreaterThanOrEqual(2);

      // Verify specific audit log exists
      expect(await auditLogsHelper.auditLogExists("Created test product")).toBe(
        true
      );
      expect(await auditLogsHelper.auditLogExists("Updated user profile")).toBe(
        true
      );
    });

    test("should display audit log details in modal", async () => {
      await createTestAuditLogInDB({
        action: "DELETE",
        resource: "Category",
        resourceId: "category-1",
        description: "Deleted test category",
        adminId: "test-admin-id",
        ipAddress: "*************",
      });

      await auditLogsHelper.goto();
      await auditLogsHelper.viewAuditLogDetails(0);

      // Verify details in modal
      await auditLogsHelper.verifyAuditLogDetails({
        action: "DELETE",
        resource: "Category",
        resourceId: "category-1",
        description: "Deleted test category",
        ipAddress: "*************",
      });

      await auditLogsHelper.closeDetailModal();
    });

    test("should handle empty audit logs state", async () => {
      await auditLogsHelper.goto();
      await auditLogsHelper.expectEmptyState();
    });
  });

  test.describe("Audit Logs Filtering", () => {
    test.beforeEach(async () => {
      // Create diverse test data for filtering
      await createTestAuditLogInDB({
        action: "CREATE",
        resource: "Product",
        description: "Created product A",
        adminId: "admin-1",
      });

      await createTestAuditLogInDB({
        action: "UPDATE",
        resource: "Product",
        description: "Updated product B",
        adminId: "admin-1",
      });

      await createTestAuditLogInDB({
        action: "DELETE",
        resource: "User",
        description: "Deleted user C",
        adminId: "admin-2",
      });

      await createTestAuditLogInDB({
        action: "CREATE",
        resource: "Category",
        description: "Created category D",
        adminId: "admin-2",
      });
    });

    test("should filter by action", async () => {
      await auditLogsHelper.goto();

      // Filter by CREATE action
      await auditLogsHelper.filterByAction("CREATE");

      const count = await auditLogsHelper.getAuditLogsCount();
      expect(count).toBe(2); // Should show only CREATE actions

      // Verify only CREATE actions are shown
      await auditLogsHelper.verifyAuditLogInTable({ action: "CREATE" }, 0);
      await auditLogsHelper.verifyAuditLogInTable({ action: "CREATE" }, 1);
    });

    test("should filter by resource", async () => {
      await auditLogsHelper.goto();

      // Filter by Product resource
      await auditLogsHelper.filterByResource("Product");

      const count = await auditLogsHelper.getAuditLogsCount();
      expect(count).toBe(2); // Should show only Product resources

      // Verify only Product resources are shown
      await auditLogsHelper.verifyAuditLogInTable({ resource: "Product" }, 0);
      await auditLogsHelper.verifyAuditLogInTable({ resource: "Product" }, 1);
    });

    test("should filter by admin", async () => {
      await auditLogsHelper.goto();

      // Filter by specific admin
      await auditLogsHelper.filterByAdmin("admin-1");

      const count = await auditLogsHelper.getAuditLogsCount();
      expect(count).toBe(2); // Should show only admin-1's actions
    });

    test("should filter by date range", async () => {
      await auditLogsHelper.goto();

      const today = new Date().toISOString().split("T")[0];
      const tomorrow = new Date(Date.now() + 24 * 60 * 60 * 1000)
        .toISOString()
        .split("T")[0];

      await auditLogsHelper.setDateRange(today, tomorrow);

      // Should show all logs created today
      const count = await auditLogsHelper.getAuditLogsCount();
      expect(count).toBeGreaterThanOrEqual(4);
    });

    test("should search audit logs", async () => {
      await auditLogsHelper.goto();

      // Search for "product"
      await auditLogsHelper.search("product");

      const count = await auditLogsHelper.getAuditLogsCount();
      expect(count).toBe(2); // Should show only logs containing "product"

      expect(await auditLogsHelper.auditLogExists("Created product A")).toBe(
        true
      );
      expect(await auditLogsHelper.auditLogExists("Updated product B")).toBe(
        true
      );
    });

    test("should clear all filters", async () => {
      await auditLogsHelper.goto();

      // Apply multiple filters
      await auditLogsHelper.filterByAction("CREATE");
      await auditLogsHelper.filterByResource("Product");

      // Verify filtered results
      let count = await auditLogsHelper.getAuditLogsCount();
      expect(count).toBe(1);

      // Clear filters
      await auditLogsHelper.clearFilters();

      // Verify all logs are shown again
      count = await auditLogsHelper.getAuditLogsCount();
      expect(count).toBe(4);
    });
  });

  test.describe("Audit Logs Sorting", () => {
    test.beforeEach(async () => {
      // Create test data with different timestamps
      const baseTime = Date.now();

      await createTestAuditLogInDB({
        action: "CREATE",
        resource: "Product",
        description: "First log",
        adminId: "admin-1",
        createdAt: new Date(baseTime - 3000),
      });

      await createTestAuditLogInDB({
        action: "UPDATE",
        resource: "User",
        description: "Second log",
        adminId: "admin-1",
        createdAt: new Date(baseTime - 2000),
      });

      await createTestAuditLogInDB({
        action: "DELETE",
        resource: "Category",
        description: "Third log",
        adminId: "admin-1",
        createdAt: new Date(baseTime - 1000),
      });
    });

    test("should sort by creation date", async () => {
      await auditLogsHelper.goto();

      // Sort by creation date ascending
      await auditLogsHelper.sortBy("createdAt", "asc");
      await auditLogsHelper.verifySortOrder("createdAt", "asc");

      // Verify order (oldest first)
      await auditLogsHelper.verifyAuditLogInTable(
        { description: "First log" },
        0
      );
      await auditLogsHelper.verifyAuditLogInTable(
        { description: "Third log" },
        2
      );

      // Sort by creation date descending
      await auditLogsHelper.sortBy("createdAt", "desc");
      await auditLogsHelper.verifySortOrder("createdAt", "desc");

      // Verify order (newest first)
      await auditLogsHelper.verifyAuditLogInTable(
        { description: "Third log" },
        0
      );
      await auditLogsHelper.verifyAuditLogInTable(
        { description: "First log" },
        2
      );
    });

    test("should sort by action", async () => {
      await auditLogsHelper.goto();

      await auditLogsHelper.sortBy("action", "asc");
      await auditLogsHelper.verifySortOrder("action", "asc");

      // Verify alphabetical order: CREATE, DELETE, UPDATE
      await auditLogsHelper.verifyAuditLogInTable({ action: "CREATE" }, 0);
      await auditLogsHelper.verifyAuditLogInTable({ action: "DELETE" }, 1);
      await auditLogsHelper.verifyAuditLogInTable({ action: "UPDATE" }, 2);
    });
  });

  test.describe("Audit Logs Pagination", () => {
    test.beforeEach(async () => {
      // Create many audit logs for pagination testing
      for (let i = 1; i <= 25; i++) {
        await createTestAuditLogInDB({
          action: "CREATE",
          resource: "Product",
          description: `Test log ${i}`,
          adminId: "admin-1",
        });
      }
    });

    test("should paginate audit logs", async () => {
      await auditLogsHelper.goto();

      // Verify first page
      let count = await auditLogsHelper.getAuditLogsCount();
      expect(count).toBe(20); // Default page size

      // Go to next page
      await auditLogsHelper.goToNextPage();

      // Verify second page
      count = await auditLogsHelper.getAuditLogsCount();
      expect(count).toBe(5); // Remaining logs

      // Go back to previous page
      await auditLogsHelper.goToPreviousPage();

      // Verify back on first page
      count = await auditLogsHelper.getAuditLogsCount();
      expect(count).toBe(20);
    });

    test("should change page size", async () => {
      await auditLogsHelper.goto();

      // Change page size to 10
      await auditLogsHelper.changePageSize(10);

      const count = await auditLogsHelper.getAuditLogsCount();
      expect(count).toBe(10);

      // Verify pagination info
      const paginationInfo = await auditLogsHelper.getPaginationInfo();
      expect(paginationInfo).toContain("1-10 of 25");
    });
  });

  test.describe("Audit Logs Export", () => {
    test.beforeEach(async () => {
      // Create test data for export
      await createTestAuditLogInDB({
        action: "CREATE",
        resource: "Product",
        description: "Export test log 1",
        adminId: "admin-1",
      });

      await createTestAuditLogInDB({
        action: "UPDATE",
        resource: "User",
        description: "Export test log 2",
        adminId: "admin-1",
      });
    });

    test("should export audit logs as CSV", async () => {
      await auditLogsHelper.goto();

      const download = await auditLogsHelper.exportAuditLogs("csv");

      expect(download.suggestedFilename()).toContain(".csv");
      expect(await download.path()).toBeTruthy();
    });

    test("should export audit logs as Excel", async () => {
      await auditLogsHelper.goto();

      const download = await auditLogsHelper.exportAuditLogs("excel");

      expect(download.suggestedFilename()).toContain(".xlsx");
      expect(await download.path()).toBeTruthy();
    });
  });

  test.describe("Real-time Updates", () => {
    test("should show new audit logs in real-time", async ({ page }) => {
      await auditLogsHelper.goto();

      // Verify initial empty state
      await auditLogsHelper.expectEmptyState();

      // Simulate creating a new audit log (this would normally happen through admin actions)
      await createTestAuditLogInDB({
        action: "CREATE",
        resource: "Product",
        description: "Real-time test log",
        adminId: "admin-1",
      });

      // Refresh to see the new log (in real implementation, this would be automatic via SSE)
      await auditLogsHelper.refresh();

      // Verify the new log appears
      await auditLogsHelper.waitForAuditLog("Real-time test log");
      expect(await auditLogsHelper.auditLogExists("Real-time test log")).toBe(
        true
      );
    });
  });

  test.describe("Permissions", () => {
    test("should allow moderators to view audit logs", async ({ page }) => {
      await loginAsModerator(page);

      await createTestAuditLogInDB({
        action: "VIEW",
        resource: "Product",
        description: "Moderator view test",
        adminId: "moderator-1",
      });

      await auditLogsHelper.goto();

      // Moderators should be able to view audit logs
      expect(await auditLogsHelper.auditLogExists("Moderator view test")).toBe(
        true
      );
    });

    test("should prevent unauthorized access", async ({ page }) => {
      // Navigate without authentication
      await page.goto("/admin/audit-logs");

      // Should redirect to login page
      await expect(page).toHaveURL(/.*\/admin\/auth\/signin/);
    });
  });

  test.describe("Error Handling", () => {
    test("should handle network errors gracefully", async ({ page }) => {
      await auditLogsHelper.goto();

      // Simulate network error by intercepting API calls
      await page.route("**/api/admin/audit-logs**", (route) => {
        route.abort("failed");
      });

      await auditLogsHelper.refresh();
      await auditLogsHelper.expectErrorState();
    });

    test("should handle loading states", async ({ page }) => {
      await auditLogsHelper.goto();

      // Simulate slow API response
      await page.route("**/api/admin/audit-logs**", async (route) => {
        await new Promise((resolve) => setTimeout(resolve, 2000));
        route.continue();
      });

      await auditLogsHelper.refresh();
      await auditLogsHelper.expectLoadingState();
    });
  });
});
