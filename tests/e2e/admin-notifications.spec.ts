/**
 * Admin Notifications E2E Tests
 * <PERSON><PERSON>m tra E2E cho hệ thống notifications
 */

import { test, expect } from "@playwright/test";
import { loginAsAdmin, loginAsModerator } from "./helpers/auth.helper";
import { NotificationsHelper, NotificationData } from "./helpers/notifications.helper";

test.describe("Admin Notifications E2E Tests", () => {
  let notificationsHelper: NotificationsHelper;

  test.beforeEach(async ({ page }) => {
    notificationsHelper = new NotificationsHelper(page);
    
    // Login as admin
    await loginAsAdmin(page);
  });

  test.describe("Notification Bell and Dropdown", () => {
    test("should display notification bell with badge", async ({ page }) => {
      // Navigate to any admin page
      await page.goto("/admin/dashboard");

      // Check if notification bell is visible
      await expect(page.locator('[data-testid="notification-bell"]')).toBeVisible();

      // Click bell to open dropdown
      await notificationsHelper.clickNotificationBell();

      // Verify dropdown is open
      await expect(page.locator('[data-testid="notification-dropdown"]')).toBeVisible();
    });

    test("should show unread notification count in badge", async () => {
      // Create test notifications via API or database
      // This would require implementing API calls or database helpers

      const badgeCount = await notificationsHelper.getBellBadgeCount();
      expect(badgeCount).toBeGreaterThanOrEqual(0);
    });

    test("should display notifications in dropdown", async () => {
      await notificationsHelper.clickNotificationBell();

      const notifications = await notificationsHelper.getDropdownNotifications();
      const count = await notifications.count();

      // Should show notifications or empty state
      expect(count).toBeGreaterThanOrEqual(0);
    });

    test("should navigate when clicking notification in dropdown", async ({ page }) => {
      await notificationsHelper.clickNotificationBell();

      // If there are notifications, click the first one
      const notifications = await notificationsHelper.getDropdownNotifications();
      const count = await notifications.count();

      if (count > 0) {
        await notificationsHelper.clickDropdownNotification(0);
        // Verify navigation occurred (this depends on the notification's actionUrl)
      }
    });
  });

  test.describe("Notifications Management Page", () => {
    test("should display notifications list", async () => {
      await notificationsHelper.goto();

      // Verify page loaded
      await expect(notificationsHelper.page.locator('[data-testid="notifications-page"]')).toBeVisible();

      // Check if notifications are displayed or empty state
      const count = await notificationsHelper.getNotificationsCount();
      expect(count).toBeGreaterThanOrEqual(0);
    });

    test("should create new notification", async () => {
      await notificationsHelper.goto();

      const notificationData: NotificationData = {
        title: "Test Notification E2E",
        message: "This is a test notification created via E2E test",
        type: "INFO",
        priority: "NORMAL",
        targetType: "ALL_ADMINS",
      };

      await notificationsHelper.createNotification(notificationData);

      // Verify success message
      await notificationsHelper.expectSuccessToast();

      // Verify notification appears in list
      expect(await notificationsHelper.notificationExists(notificationData.title)).toBe(true);
    });

    test("should mark notification as read/unread", async () => {
      await notificationsHelper.goto();

      const initialCount = await notificationsHelper.getNotificationsCount();
      
      if (initialCount > 0) {
        // Mark first notification as read
        await notificationsHelper.markAsRead(0);
        await notificationsHelper.expectSuccessToast();

        // Mark it back as unread
        await notificationsHelper.markAsUnread(0);
        await notificationsHelper.expectSuccessToast();
      }
    });

    test("should delete notification", async () => {
      await notificationsHelper.goto();

      const initialCount = await notificationsHelper.getNotificationsCount();
      
      if (initialCount > 0) {
        await notificationsHelper.deleteNotification(0);
        await notificationsHelper.expectSuccessToast();

        // Verify count decreased
        const newCount = await notificationsHelper.getNotificationsCount();
        expect(newCount).toBe(initialCount - 1);
      }
    });

    test("should perform bulk operations", async () => {
      await notificationsHelper.goto();

      const initialCount = await notificationsHelper.getNotificationsCount();
      
      if (initialCount > 0) {
        // Bulk mark as read
        await notificationsHelper.bulkMarkAsRead();
        await notificationsHelper.expectSuccessToast();

        // Bulk delete
        await notificationsHelper.bulkDelete();
        await notificationsHelper.expectSuccessToast();
      }
    });
  });

  test.describe("Notification Filtering and Search", () => {
    test("should filter notifications by type", async () => {
      await notificationsHelper.goto();

      // Filter by WARNING type
      await notificationsHelper.filterByType("WARNING");

      // Verify only WARNING notifications are shown
      const count = await notificationsHelper.getNotificationsCount();
      // This would need actual test data to verify properly
    });

    test("should filter notifications by priority", async () => {
      await notificationsHelper.goto();

      // Filter by HIGH priority
      await notificationsHelper.filterByPriority("HIGH");

      // Verify only HIGH priority notifications are shown
      const count = await notificationsHelper.getNotificationsCount();
      // This would need actual test data to verify properly
    });

    test("should filter notifications by read status", async () => {
      await notificationsHelper.goto();

      // Filter unread only
      await notificationsHelper.filterByReadStatus("unread");

      // Verify only unread notifications are shown
      const count = await notificationsHelper.getNotificationsCount();
      // This would need actual test data to verify properly
    });

    test("should search notifications", async () => {
      await notificationsHelper.goto();

      // Search for specific text
      await notificationsHelper.search("test");

      // Verify search results
      const count = await notificationsHelper.getNotificationsCount();
      // This would need actual test data to verify properly
    });

    test("should clear all filters", async () => {
      await notificationsHelper.goto();

      // Apply multiple filters
      await notificationsHelper.filterByType("INFO");
      await notificationsHelper.filterByPriority("NORMAL");

      // Clear filters
      await notificationsHelper.clearFilters();

      // Verify all notifications are shown again
      const count = await notificationsHelper.getNotificationsCount();
      // This would need actual test data to verify properly
    });
  });

  test.describe("Notification Settings", () => {
    test("should update notification preferences", async () => {
      await notificationsHelper.gotoSettings();

      // Update preferences
      await notificationsHelper.updatePreferences({
        emailEnabled: true,
        quietHoursEnabled: true,
        quietHoursStart: "22:00",
        quietHoursEnd: "08:00",
        notificationTypes: ["WARNING", "ERROR"],
      });

      // Verify success message
      await notificationsHelper.expectSuccessToast();
    });

    test("should test email notification", async () => {
      await notificationsHelper.gotoSettings();

      // Test email notification
      await notificationsHelper.testEmailNotification();

      // Verify success or error message
      // This depends on email configuration
    });
  });

  test.describe("Real-time Notifications", () => {
    test("should receive real-time notifications", async ({ page }) => {
      // Navigate to dashboard
      await page.goto("/admin/dashboard");

      // This test would require triggering a server-side event
      // that creates a notification and sends it via SSE
      
      // For now, we can test the SSE connection setup
      await expect(page.locator('[data-testid="notification-bell"]')).toBeVisible();
    });

    test("should update notification count in real-time", async ({ page }) => {
      await page.goto("/admin/dashboard");

      const initialBadgeCount = await notificationsHelper.getBellBadgeCount();

      // This would require triggering a real notification event
      // For testing purposes, we can simulate this by creating a notification
      // and checking if the badge updates

      // In a real test, you would:
      // 1. Create a notification via API
      // 2. Wait for SSE event
      // 3. Verify badge count increased
    });
  });

  test.describe("Notification Permissions", () => {
    test("should show appropriate notifications for admin role", async ({ page }) => {
      await loginAsAdmin(page);
      await notificationsHelper.goto();

      // Admins should see all notifications targeted to them
      const count = await notificationsHelper.getNotificationsCount();
      expect(count).toBeGreaterThanOrEqual(0);
    });

    test("should show appropriate notifications for moderator role", async ({ page }) => {
      await loginAsModerator(page);
      await notificationsHelper.goto();

      // Moderators should see notifications targeted to their role
      const count = await notificationsHelper.getNotificationsCount();
      expect(count).toBeGreaterThanOrEqual(0);
    });

    test("should prevent unauthorized access to notification management", async ({ page }) => {
      // Navigate without authentication
      await page.goto("/admin/notifications");

      // Should redirect to login page
      await expect(page).toHaveURL(/.*\/admin\/auth\/signin/);
    });
  });

  test.describe("Notification Pagination", () => {
    test("should paginate notifications", async () => {
      await notificationsHelper.goto();

      const initialCount = await notificationsHelper.getNotificationsCount();

      if (initialCount >= 20) { // Assuming page size is 20
        // Go to next page
        await notificationsHelper.goToNextPage();

        // Verify we're on next page
        const nextPageCount = await notificationsHelper.getNotificationsCount();
        expect(nextPageCount).toBeGreaterThanOrEqual(0);

        // Go back to previous page
        await notificationsHelper.goToPreviousPage();

        // Verify we're back on first page
        const backCount = await notificationsHelper.getNotificationsCount();
        expect(backCount).toBe(initialCount);
      }
    });
  });

  test.describe("Error Handling", () => {
    test("should handle network errors gracefully", async ({ page }) => {
      await notificationsHelper.goto();

      // Simulate network error
      await page.route("**/api/admin/notifications**", route => {
        route.abort("failed");
      });

      await notificationsHelper.refresh();
      // Should show error state or handle gracefully
    });

    test("should handle loading states", async ({ page }) => {
      await notificationsHelper.goto();

      // Simulate slow API response
      await page.route("**/api/admin/notifications**", async route => {
        await new Promise(resolve => setTimeout(resolve, 2000));
        route.continue();
      });

      await notificationsHelper.refresh();
      await notificationsHelper.expectLoadingState();
    });

    test("should handle empty state", async () => {
      await notificationsHelper.goto();

      // If no notifications exist, should show empty state
      const count = await notificationsHelper.getNotificationsCount();
      if (count === 0) {
        await notificationsHelper.expectEmptyState();
      }
    });
  });

  test.describe("Notification Form Validation", () => {
    test("should validate required fields when creating notification", async () => {
      await notificationsHelper.goto();

      // Try to create notification without required fields
      await notificationsHelper.page.click('[data-testid="create-notification-btn"]');
      await notificationsHelper.page.click('[data-testid="submit-notification-btn"]');

      // Should show validation errors
      await expect(notificationsHelper.page.locator('[data-testid="title-error"]')).toBeVisible();
      await expect(notificationsHelper.page.locator('[data-testid="message-error"]')).toBeVisible();
    });

    test("should validate notification data types", async () => {
      await notificationsHelper.goto();

      const invalidData: NotificationData = {
        title: "", // Empty title should be invalid
        message: "", // Empty message should be invalid
        type: "INFO",
        priority: "NORMAL",
        targetType: "ALL_ADMINS",
      };

      await notificationsHelper.page.click('[data-testid="create-notification-btn"]');
      
      // Fill with invalid data
      await notificationsHelper.page.fill('[data-testid="title-input"]', invalidData.title);
      await notificationsHelper.page.fill('[data-testid="message-input"]', invalidData.message);
      
      await notificationsHelper.page.click('[data-testid="submit-notification-btn"]');

      // Should show validation errors
      await notificationsHelper.expectErrorToast();
    });
  });
});
