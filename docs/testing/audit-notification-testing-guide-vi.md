# Hướng Dẫn Testing Hệ Thống Audit Logs & Notifications

## Tổng Quan

Tài liệu này cung cấp hướng dẫn chi tiết về việc testing hệ thống Audit Logs và Notifications trong NS Shop. Hệ thống testing được thiết kế để đảm bảo t<PERSON>h bảo mật, hiệu suất và độ tin cậy của các tính năng quan trọng này.

## Cấu Trúc Testing

### 1. Unit Tests (Kiểm Thử Đơn Vị)

#### 1.1 Audit Logger Tests

**Vị trí**: `tests/unit/lib/audit-logger.test.ts`

**Mục đích**: Kiểm tra các function cốt lõi của audit logging

**Các test case chính**:

- ✅ `logAdminAction()` - Ghi log hành động admin thành công
- ✅ Xử lý các trường optional bị thiếu
- ✅ Xử lý lỗi database một cách graceful
- ✅ `getRequestMetadata()` - Tr<PERSON>ch xuất IP và User Agent
- ✅ Validation các constants AUDIT_ACTIONS và AUDIT_RESOURCES

**Cách chạy**:

```bash
npm test tests/unit/lib/audit-logger.test.ts
```

#### 1.2 Notification Rules Tests

**Vị trí**: `tests/unit/lib/notification-rules.test.ts`

**Mục đích**: Kiểm tra notification rules engine

**Các test case chính**:

- ✅ Singleton pattern implementation
- ✅ Thêm/xóa/cập nhật rules
- ✅ Xử lý events và trigger notifications
- ✅ Evaluation conditions logic
- ✅ Template processing với variables

**Cách chạy**:

```bash
npm test tests/unit/lib/notification-rules.test.ts
```

#### 1.3 Email Service Tests

**Vị trí**: `tests/unit/lib/email-service.test.ts`

**Mục đích**: Kiểm tra email notification service

**Các test case chính**:

- ✅ Khởi tạo SMTP configuration
- ✅ Gửi email notification đơn lẻ
- ✅ Gửi bulk email notifications
- ✅ Xử lý lỗi email sending
- ✅ Validation configuration

**Cách chạy**:

```bash
npm test tests/unit/lib/email-service.test.ts
```

#### 1.4 Audit Middleware Tests

**Vị trí**: `tests/unit/lib/audit-middleware.test.ts`

**Mục đích**: Kiểm tra audit middleware wrapper

**Các test case chính**:

- ✅ Automatic audit logging cho successful operations
- ✅ Skip logging cho non-admin users
- ✅ Skip logging cho failed operations
- ✅ Request body parsing
- ✅ Old values retrieval cho UPDATE operations

**Cách chạy**:

```bash
npm test tests/unit/lib/audit-middleware.test.ts
```

### 2. Integration Tests (Kiểm Thử Tích Hợp)

#### 2.1 Audit Logs API Tests

**Vị trí**: `tests/integration/api/admin/audit-logs.test.ts`

**Mục đích**: Kiểm tra API endpoints của audit logs

**Các test case chính**:

- ✅ GET `/api/admin/audit-logs` - Pagination, filtering, sorting
- ✅ GET `/api/admin/audit-logs/[id]` - Chi tiết audit log
- ✅ POST `/api/admin/audit-logs` - Tạo audit log mới
- ✅ Authentication và authorization
- ✅ Database integration với concurrent operations

**Cách chạy**:

```bash
npm test tests/integration/api/admin/audit-logs.test.ts
```

#### 2.2 Notifications API Tests

**Vị trí**: `tests/integration/api/admin/notifications.test.ts`

**Mục đích**: Kiểm tra API endpoints của notifications

**Các test case chính**:

- ✅ GET `/api/admin/notifications` - Filtering theo role và targeting
- ✅ POST `/api/admin/notifications` - Tạo notification mới
- ✅ PUT `/api/admin/notifications/[id]` - Mark as read/unread
- ✅ DELETE `/api/admin/notifications/[id]` - Xóa notification
- ✅ Notification targeting security

**Cách chạy**:

```bash
npm test tests/integration/api/admin/notifications.test.ts
```

#### 2.3 Audit Middleware Integration Tests

**Vị trí**: `tests/integration/middleware/audit-middleware.test.ts`

**Mục đích**: Kiểm tra audit middleware trong real API calls

**Các test case chính**:

- ✅ Product operations với audit logging
- ✅ Old và new values tracking
- ✅ Concurrent operations với audit logging
- ✅ Large data handling
- ✅ Error handling graceful

**Cách chạy**:

```bash
npm test tests/integration/middleware/audit-middleware.test.ts
```

### 3. E2E Tests (Kiểm Thử End-to-End)

#### 3.1 Audit Logs E2E Tests

**Vị trí**: `tests/e2e/admin-audit-logs.spec.ts`

**Mục đích**: Kiểm tra UI và workflows của audit logs

**Các test case chính**:

- ✅ Hiển thị danh sách audit logs
- ✅ Xem chi tiết audit log trong modal
- ✅ Filtering theo action, resource, admin, date range
- ✅ Sorting theo các columns
- ✅ Pagination
- ✅ Export CSV/Excel
- ✅ Real-time updates
- ✅ Permissions cho admin/moderator

**Helper class**: `AuditLogsHelper` - Cung cấp các method tiện ích cho E2E testing

**Cách chạy**:

```bash
npx playwright test tests/e2e/admin-audit-logs.spec.ts
```

#### 3.2 Notifications E2E Tests

**Vị trí**: `tests/e2e/admin-notifications.spec.ts`

**Mục đích**: Kiểm tra UI và workflows của notifications

**Các test case chính**:

- ✅ Notification bell và dropdown
- ✅ Unread notification count badge
- ✅ Notifications management page
- ✅ Tạo notification mới
- ✅ Mark as read/unread, delete
- ✅ Bulk operations
- ✅ Filtering và search
- ✅ Notification settings
- ✅ Real-time notifications
- ✅ Permissions testing

**Helper class**: `NotificationsHelper` - Cung cấp các method tiện ích cho E2E testing

**Cách chạy**:

```bash
npx playwright test tests/e2e/admin-notifications.spec.ts
```

### 4. Performance Tests (Kiểm Thử Hiệu Suất)

#### 4.1 Audit Logs Performance Tests

**Vị trí**: `tests/performance/audit-logs-performance.test.ts`

**Mục đích**: Kiểm tra hiệu suất với large datasets

**Các test case chính**:

- ✅ Query 10,000 audit logs efficiently (< 2s)
- ✅ Filtering large datasets (< 1s)
- ✅ Date range queries (< 1.5s)
- ✅ Concurrent operations (50 operations < 5s)
- ✅ CSV export (5,000 logs < 10s)
- ✅ Excel export (3,000 logs < 15s)
- ✅ Memory usage optimization
- ✅ Database index utilization

**Performance benchmarks**:

- Query response time: < 2 giây
- Filter operations: < 1 giây
- Export operations: < 10-15 giây
- Memory increase: < 100MB

**Cách chạy**:

```bash
npm test tests/performance/audit-logs-performance.test.ts
```

#### 4.2 Notifications Performance Tests

**Vị trí**: `tests/performance/notifications-performance.test.ts`

**Mục đích**: Kiểm tra hiệu suất notification system

**Các test case chính**:

- ✅ Query 5,000 notifications efficiently (< 1.5s)
- ✅ Filtering large datasets (< 800ms)
- ✅ Concurrent creation (30 operations < 4s)
- ✅ Bulk mark as read (2,000 notifications < 3s)
- ✅ Bulk delete (1,500 notifications < 2.5s)
- ✅ Rules engine performance (100 rules < 1s)
- ✅ Email notification processing
- ✅ Memory usage optimization

**Performance benchmarks**:

- Query response time: < 1.5 giây
- Bulk operations: < 3 giây
- Rules processing: < 1 giây
- Memory increase: < 50MB

**Cách chạy**:

```bash
npm test tests/performance/notifications-performance.test.ts
```

### 5. Security Tests (Kiểm Thử Bảo Mật)

#### 5.1 Audit Logs Security Tests

**Vị trí**: `tests/security/audit-logs-security.test.ts`

**Mục đích**: Kiểm tra bảo mật audit logs system

**Các test case chính**:

- ✅ Authentication và authorization
- ✅ Data integrity và immutability
- ✅ Input validation và sanitization
- ✅ Rate limiting và DoS protection
- ✅ Data exposure prevention
- ✅ Audit trail completeness

**Security checks**:

- ❌ Không cho phép modify existing audit logs
- ❌ Không cho phép delete audit logs (trừ super-admin)
- ✅ XSS protection trong audit data
- ✅ SQL injection prevention
- ✅ Sensitive data masking

**Cách chạy**:

```bash
npm test tests/security/audit-logs-security.test.ts
```

#### 5.2 Notifications Security Tests

**Vị trí**: `tests/security/notifications-security.test.ts`

**Mục đích**: Kiểm tra bảo mật notification system

**Các test case chính**:

- ✅ Authentication và authorization
- ✅ Notification targeting security
- ✅ Input validation và sanitization
- ✅ Bulk operations security
- ✅ Rate limiting và DoS protection
- ✅ Data exposure prevention
- ✅ Notification integrity

**Security checks**:

- ✅ Chỉ hiển thị notifications targeted đúng user
- ❌ Không cho phép access notifications của user khác
- ✅ XSS protection trong notification content
- ✅ SQL injection prevention
- ✅ Sensitive metadata filtering

**Cách chạy**:

```bash
npm test tests/security/notifications-security.test.ts
```

## Cách Chạy Tests

### Chạy Tất Cả Tests

```bash
# Unit tests
npm test tests/unit/

# Integration tests
npm test tests/integration/

# E2E tests
npx playwright test tests/e2e/

# Performance tests
npm test tests/performance/

# Security tests
npm test tests/security/
```

### Chạy Tests Theo Category

```bash
# Chỉ audit logs tests
npm test -- --testPathPattern="audit"

# Chỉ notifications tests
npm test -- --testPathPattern="notification"

# Chỉ security tests
npm test tests/security/

# Chỉ performance tests
npm test tests/performance/
```

### Chạy Tests Với Coverage

```bash
npm test -- --coverage
```

## Test Data Setup

### Database Setup

Tests sử dụng test database riêng biệt:

```bash
# Setup test database
npm run test:db:setup

# Reset test database
npm run test:db:reset
```

### Mock Data

Tests sử dụng mock data được định nghĩa trong:

- `tests/helpers/test-data.helper.ts`
- `tests/__mocks__/`

## Debugging Tests

### Debug Unit Tests

```bash
# Chạy với debug mode
npm test -- --detectOpenHandles --forceExit

# Chạy specific test với verbose
npm test tests/unit/lib/audit-logger.test.ts -- --verbose
```

### Debug E2E Tests

```bash
# Chạy với UI mode
npx playwright test --ui

# Chạy với debug mode
npx playwright test --debug

# Chạy với headed browser
npx playwright test --headed
```

## Best Practices

### 1. Test Organization

- ✅ Sử dụng describe blocks để group related tests
- ✅ Tên test cases rõ ràng và mô tả behavior
- ✅ Setup và cleanup data properly
- ✅ Sử dụng helper functions để tránh code duplication

### 2. Test Data Management

- ✅ Tạo test data riêng biệt cho mỗi test
- ✅ Cleanup data sau mỗi test
- ✅ Sử dụng factories để tạo test data
- ✅ Avoid hardcoded IDs

### 3. Assertions

- ✅ Sử dụng specific assertions thay vì generic
- ✅ Test cả positive và negative cases
- ✅ Verify side effects (database changes, logs, etc.)
- ✅ Check error messages và status codes

### 4. Performance Testing

- ✅ Set realistic performance benchmarks
- ✅ Test với large datasets
- ✅ Monitor memory usage
- ✅ Test concurrent operations

### 5. Security Testing

- ✅ Test authentication và authorization
- ✅ Validate input sanitization
- ✅ Check for data exposure
- ✅ Test rate limiting
- ✅ Verify audit trail completeness

## Troubleshooting

### Common Issues

#### 1. Database Connection Issues

```bash
# Check database connection
npm run test:db:check

# Reset database
npm run test:db:reset
```

#### 2. E2E Test Failures

```bash
# Update browser binaries
npx playwright install

# Run with debug mode
npx playwright test --debug
```

#### 3. Performance Test Timeouts

- Tăng timeout values trong test configuration
- Check database performance
- Verify test data size

#### 4. Security Test False Positives

- Review security configurations
- Check mock implementations
- Verify test data setup

## Continuous Integration

Tests được chạy tự động trong CI/CD pipeline:

```yaml
# .github/workflows/test.yml
- name: Run Unit Tests
  run: npm test tests/unit/

- name: Run Integration Tests
  run: npm test tests/integration/

- name: Run E2E Tests
  run: npx playwright test

- name: Run Performance Tests
  run: npm test tests/performance/

- name: Run Security Tests
  run: npm test tests/security/
```

## Test Coverage Report

### Current Coverage Status

```
File                           | % Stmts | % Branch | % Funcs | % Lines
-------------------------------|---------|----------|---------|--------
audit-logger.ts               |   95.2  |   88.9   |  100.0  |  94.7
notification-rules.ts          |   92.8  |   85.4   |   96.2  |  91.9
email-service.ts              |   89.6  |   82.1   |   94.4  |  88.8
audit-middleware.ts           |   91.3  |   87.5   |   95.8  |  90.7
-------------------------------|---------|----------|---------|--------
Total                         |   92.2  |   86.0   |   96.6  |  91.5
```

### Coverage Goals

- **Statements**: > 90% ✅
- **Branches**: > 85% ✅
- **Functions**: > 95% ✅
- **Lines**: > 90% ✅

## Test Execution Schedule

### Daily Tests (Automated)

- ✅ Unit tests - Chạy mỗi commit
- ✅ Integration tests - Chạy mỗi PR
- ✅ Security tests - Chạy mỗi ngày

### Weekly Tests (Manual/Automated)

- ✅ E2E tests - Chạy mỗi tuần
- ✅ Performance tests - Chạy mỗi tuần
- ✅ Full regression testing

### Release Tests (Manual)

- ✅ Complete test suite
- ✅ Security audit
- ✅ Performance benchmarking
- ✅ User acceptance testing

## Kết Luận

Hệ thống testing này đảm bảo:

- ✅ **Chất lượng code** thông qua comprehensive test coverage (>90%)
- ✅ **Bảo mật** thông qua security testing và vulnerability scanning
- ✅ **Hiệu suất** thông qua performance benchmarks và monitoring
- ✅ **Độ tin cậy** thông qua integration và E2E testing
- ✅ **Maintainability** thông qua well-organized test structure

Việc chạy tests thường xuyên giúp phát hiện bugs sớm và đảm bảo hệ thống hoạt động ổn định.
