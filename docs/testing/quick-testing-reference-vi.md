# Quick Testing Reference - NS Shop Audit & Notifications

## Cheat Sheet cho Developers

### 🚀 Quick Commands

```bash
# Ch<PERSON><PERSON> tất cả tests
npm test

# Chạy specific test file
npm test tests/unit/lib/audit-logger.test.ts

# Chạy tests với coverage
npm test -- --coverage

# Chạy E2E tests
npx playwright test

# Chạy E2E với UI
npx playwright test --ui

# Debug specific E2E test
npx playwright test tests/e2e/admin-audit-logs.spec.ts --debug
```

### 📁 Test File Structure

```
tests/
├── unit/                    # Unit tests
│   └── lib/
│       ├── audit-logger.test.ts
│       ├── notification-rules.test.ts
│       ├── email-service.test.ts
│       └── audit-middleware.test.ts
├── integration/             # Integration tests
│   ├── api/admin/
│   │   ├── audit-logs.test.ts
│   │   └── notifications.test.ts
│   └── middleware/
│       └── audit-middleware.test.ts
├── e2e/                     # End-to-end tests
│   ├── admin-audit-logs.spec.ts
│   ├── admin-notifications.spec.ts
│   └── helpers/
│       ├── audit-logs.helper.ts
│       └── notifications.helper.ts
├── performance/             # Performance tests
│   ├── audit-logs-performance.test.ts
│   └── notifications-performance.test.ts
└── security/               # Security tests
    ├── audit-logs-security.test.ts
    └── notifications-security.test.ts
```

### 🧪 Test Categories

| Category | Purpose | When to Run |
|----------|---------|-------------|
| **Unit** | Test individual functions | Every commit |
| **Integration** | Test API endpoints | Every PR |
| **E2E** | Test user workflows | Weekly/Release |
| **Performance** | Test with large data | Weekly |
| **Security** | Test security measures | Daily/Release |

### 🎯 Test Patterns

#### Unit Test Pattern
```typescript
describe("Component Name", () => {
  beforeEach(() => {
    // Setup mocks
    jest.clearAllMocks();
  });

  it("should do something specific", async () => {
    // Arrange
    const input = { /* test data */ };
    
    // Act
    const result = await functionUnderTest(input);
    
    // Assert
    expect(result).toEqual(expectedOutput);
  });
});
```

#### Integration Test Pattern
```typescript
describe("API Endpoint", () => {
  beforeEach(async () => {
    // Clean database
    await prisma.model.deleteMany();
    // Setup test data
    await createTestData();
  });

  it("should handle request correctly", async () => {
    const request = new NextRequest(url, options);
    const response = await handler(request);
    
    expect(response.status).toBe(200);
    // Verify database changes
  });
});
```

#### E2E Test Pattern
```typescript
test("should complete user workflow", async ({ page }) => {
  // Navigate
  await page.goto("/admin/audit-logs");
  
  // Interact
  await page.click('[data-testid="filter-btn"]');
  
  // Assert
  await expect(page.locator('[data-testid="results"]')).toBeVisible();
});
```

### 🔧 Common Test Utilities

#### Mock Session
```typescript
const mockAdminSession = {
  user: {
    id: "admin-123",
    type: "admin",
    role: "ADMIN",
    name: "Test Admin",
  },
};

mockGetServerSession.mockResolvedValue(mockAdminSession);
```

#### Create Test Data
```typescript
const testAuditLog = await prisma.auditLog.create({
  data: {
    action: "CREATE",
    resource: "Product",
    resourceId: "product-1",
    description: "Test action",
    adminId: "admin-123",
    ipAddress: "***********",
    userAgent: "Test Agent",
  },
});
```

#### E2E Helper Usage
```typescript
const auditLogsHelper = new AuditLogsHelper(page);
await auditLogsHelper.goto();
await auditLogsHelper.filterByAction("CREATE");
expect(await auditLogsHelper.getAuditLogsCount()).toBe(1);
```

### 🐛 Debugging Tips

#### Unit Test Debugging
```bash
# Run with verbose output
npm test -- --verbose

# Run single test
npm test -- --testNamePattern="should log admin action"

# Debug with Node inspector
node --inspect-brk node_modules/.bin/jest --runInBand
```

#### E2E Test Debugging
```bash
# Run with browser visible
npx playwright test --headed

# Pause on failure
npx playwright test --pause-on-failure

# Generate trace
npx playwright test --trace on
```

### 📊 Performance Benchmarks

| Operation | Target Time | Max Memory |
|-----------|-------------|------------|
| Query 10K audit logs | < 2s | < 100MB |
| Filter large dataset | < 1s | < 50MB |
| Export 5K logs (CSV) | < 10s | < 200MB |
| Bulk notification ops | < 3s | < 75MB |
| Rules engine (100 rules) | < 1s | < 25MB |

### 🔒 Security Checklist

- [ ] Authentication required for all admin endpoints
- [ ] Authorization based on user role
- [ ] Input validation and sanitization
- [ ] XSS prevention in output
- [ ] SQL injection prevention
- [ ] Rate limiting for bulk operations
- [ ] Audit trail completeness
- [ ] Sensitive data masking

### 🚨 Common Issues & Solutions

#### Database Connection Issues
```bash
# Check connection
npm run test:db:check

# Reset test database
npm run test:db:reset
```

#### E2E Test Timeouts
```typescript
// Increase timeout
test.setTimeout(60000);

// Wait for specific condition
await page.waitForSelector('[data-testid="element"]', { timeout: 10000 });
```

#### Mock Issues
```typescript
// Clear mocks between tests
afterEach(() => {
  jest.clearAllMocks();
});

// Reset modules
beforeEach(() => {
  jest.resetModules();
});
```

### 📈 Coverage Goals

| Metric | Target | Current |
|--------|--------|---------|
| Statements | > 90% | 92.2% ✅ |
| Branches | > 85% | 86.0% ✅ |
| Functions | > 95% | 96.6% ✅ |
| Lines | > 90% | 91.5% ✅ |

### 🔄 CI/CD Integration

```yaml
# Test stages in pipeline
stages:
  - unit-tests      # Fast feedback
  - integration     # API validation
  - security        # Security checks
  - e2e            # User workflows
  - performance    # Load testing
```

### 📝 Test Writing Guidelines

#### DO ✅
- Write descriptive test names
- Test both success and error cases
- Use data-testid for E2E selectors
- Clean up test data after each test
- Mock external dependencies
- Test edge cases and boundary conditions

#### DON'T ❌
- Test implementation details
- Use hardcoded delays (use waitFor instead)
- Share state between tests
- Test multiple things in one test
- Ignore test failures
- Skip cleanup in afterEach

### 🎯 Quick Test Commands by Feature

#### Audit Logs
```bash
# Unit tests
npm test tests/unit/lib/audit-logger.test.ts

# Integration tests
npm test tests/integration/api/admin/audit-logs.test.ts

# E2E tests
npx playwright test tests/e2e/admin-audit-logs.spec.ts

# Performance tests
npm test tests/performance/audit-logs-performance.test.ts

# Security tests
npm test tests/security/audit-logs-security.test.ts
```

#### Notifications
```bash
# Unit tests
npm test tests/unit/lib/notification-rules.test.ts

# Integration tests
npm test tests/integration/api/admin/notifications.test.ts

# E2E tests
npx playwright test tests/e2e/admin-notifications.spec.ts

# Performance tests
npm test tests/performance/notifications-performance.test.ts

# Security tests
npm test tests/security/notifications-security.test.ts
```

### 🔍 Test Data Patterns

#### Minimal Test Data
```typescript
const minimalAuditLog = {
  action: "CREATE",
  resource: "Product",
  adminId: "admin-123",
};
```

#### Complete Test Data
```typescript
const completeAuditLog = {
  action: "UPDATE",
  resource: "Product",
  resourceId: "product-1",
  oldValues: { name: "Old Name" },
  newValues: { name: "New Name" },
  description: "Updated product name",
  adminId: "admin-123",
  ipAddress: "***********",
  userAgent: "Test Agent",
};
```

### 📞 Support

- **Documentation**: `/docs/testing/`
- **Issues**: Create GitHub issue với label `testing`
- **Questions**: Slack channel `#testing-support`

---

**Remember**: Tests là documentation sống của code. Viết tests tốt = code tốt! 🚀
